
import { API_KEY_DEV, API_KEY_PROD, API_URL_DEV, API_URL_PROD, ENVIRONMENT } from '$env/static/private';

import { json } from '@sveltejs/kit';
import { connectToDatabase } from '$lib/mongodb';

// Rate limiting storage (in production, use Redis or database)
const rateLimitMap = new Map();

// Rate limiting function - counts "Get Recommendations" button clicks, not HTTP requests
function checkRateLimit(userEmail: string): boolean {
	const now = Date.now();
	const userActions = rateLimitMap.get(userEmail) || [];

	// Remove actions older than 1 hour (3600000 ms)
	const recentActions = userActions.filter((time: number) => now - time < 3600000);

	// Allow max 50 "Get Recommendations" actions per hour per user
	if (recentActions.length >= 50) {
		return false;
	}

	recentActions.push(now);
	rateLimitMap.set(userEmail, recentActions);
	return true;
}

// User authentication function
async function validateUser(request: Request) {
	const userEmail = request.headers.get('x-user-email');

	if (!userEmail) {
		return null;
	}

	try {
		const db = await connectToDatabase();
		const user = await db.collection('users').findOne({
			email: userEmail,
			isEnabled: true
		});

		return user;
	} catch (error) {
		console.error('Error validating user:', error);
		return null;
	}
}

export async function POST({ request }: { request: any }) {
	// 1. Validate user authentication
	const user = await validateUser(request);
	if (!user) {
		return new Response('Unauthorized: Please login to get recommendations', { status: 401 });
	}

	// 2. Check rate limiting
	if (!checkRateLimit(user.email)) {
		return new Response('Rate limit exceeded: Maximum 50 requests per hour allowed', { status: 429 });
	}

	const { searched, userCountry } = await request.json();

	// Get user IP address
	let userIP =
		request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
		request.headers.get('cf-connecting-ip') ||
		request.headers.get('x-real-ip') ||
		(request as any).ip || // Some frameworks attach ip directly
		'127.0.0.1';

	// Determine which API key and URL to use based on environment
	const apiKey = ENVIRONMENT === 'PROD' ? API_KEY_PROD : API_KEY_DEV;
	const baseUrl = ENVIRONMENT === 'PROD' ? API_URL_PROD : API_URL_DEV;
	const apiUrl = `${baseUrl}/api/openai`;

	// Send POST request with searched text, user IP, and user country
	const res = await fetch(apiUrl, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			'x-api-key': apiKey
		},
		body: JSON.stringify({
			searched: searched,
			userIP: userIP,
			userCountry: userCountry || 'US' // Default to US if not provided
		})
	});

	if (!res.ok) {
		return new Response(`API request failed: ${res.statusText}`, { status: res.status });
	}

	const details = await res.json();

	return json(details);
}

