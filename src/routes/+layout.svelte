<script lang="ts">
	import '../app.css';
	import { dev } from '$app/environment';
	import { onMount } from 'svelte';
	import CookieConsent from '$lib/CookieConsent.svelte';

	let analyticsLoaded = false;

	// Handle cookie consent
	function handleCookieConsent(event: CustomEvent) {
		const preferences = event.detail;

		// Only load analytics if user consented and we're in production
		if (!dev && preferences.analytics && !analyticsLoaded) {
			loadAnalytics();
		}
	}

	async function loadAnalytics() {
		try {
			const { inject } = await import('@vercel/analytics');
			inject();
			analyticsLoaded = true;
		} catch (error) {
			console.warn('Analytics failed to load:', error);
		}
	}

	// Check for existing consent on mount
	onMount(() => {
		if (!dev) {
			const consent = localStorage.getItem('cookie-consent');
			if (consent) {
				try {
					const preferences = JSON.parse(consent);
					if (preferences.analytics && !analyticsLoaded) {
						loadAnalytics();
					}
				} catch (e) {
					// Invalid consent data, will be handled by <PERSON>ieConsent component
				}
			}
		}
	});
</script>

<div class="min-h-screen w-full">
	<slot />
	<CookieConsent on:consent={handleCookieConsent} />
</div>
