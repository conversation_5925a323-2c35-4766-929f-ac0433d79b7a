<script>
	import { onMount } from 'svelte';
	import { fade, slide } from 'svelte/transition';
	import { createEventDispatcher } from 'svelte';

	const dispatch = createEventDispatcher();

	let showBanner = false;
	let showDetails = false;

	// Cookie preferences
	let preferences = {
		necessary: true, // Always true, can't be disabled
		analytics: false,
		functional: false
	};

	onMount(() => {
		// Check if user has already made a choice
		const consent = localStorage.getItem('cookie-consent');
		if (!consent) {
			showBanner = true;
		} else {
			// Load saved preferences
			try {
				const saved = JSON.parse(consent);
				preferences = { ...preferences, ...saved };
				// Dispatch consent status to parent
				dispatch('consent', preferences);
			} catch (e) {
				// If parsing fails, show banner again
				showBanner = true;
			}
		}
	});

	function acceptAll() {
		preferences = {
			necessary: true,
			analytics: true,
			functional: true
		};
		savePreferences();
	}

	function acceptNecessary() {
		preferences = {
			necessary: true,
			analytics: false,
			functional: false
		};
		savePreferences();
	}

	function saveCustomPreferences() {
		savePreferences();
	}

	function savePreferences() {
		localStorage.setItem('cookie-consent', JSON.stringify(preferences));
		showBanner = false;
		showDetails = false;
		// Dispatch consent to parent component
		dispatch('consent', preferences);
	}

	function toggleDetails() {
		showDetails = !showDetails;
	}

	function resetConsent() {
		localStorage.removeItem('cookie-consent');
		showBanner = true;
		showDetails = false;
		preferences = {
			necessary: true,
			analytics: false,
			functional: false
		};
	}

	// Export function to allow manual reset
	export { resetConsent };
</script>

{#if showBanner}
	<div 
		class="fixed bottom-0 left-0 right-0 z-50 bg-slate-900/95 backdrop-blur-sm border-t border-white/20 p-4 shadow-lg"
		transition:slide={{ duration: 300 }}
	>
		<div class="max-w-6xl mx-auto">
			{#if !showDetails}
				<!-- Simple Banner -->
				<div class="flex flex-col md:flex-row items-start md:items-center gap-4">
					<div class="flex-1">
						<h3 class="text-white font-semibold mb-2">🍪 We use cookies</h3>
						<p class="text-white/80 text-sm">
							We use cookies to enhance your experience, analyze site usage, and provide personalized recommendations. 
							By continuing to use our site, you consent to our use of cookies.
							<a href="/privacy" target="_blank" class="text-turquoise-400 hover:text-turquoise-300 underline ml-1">
								Learn more in our Privacy Policy
							</a>
						</p>
					</div>
					
					<div class="flex flex-col sm:flex-row gap-2 min-w-fit">
						<button
							on:click={toggleDetails}
							class="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white text-sm rounded-md transition-colors"
						>
							Customize
						</button>
						<button
							on:click={acceptNecessary}
							class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded-md transition-colors"
						>
							Necessary Only
						</button>
						<button
							on:click={acceptAll}
							class="px-4 py-2 bg-turquoise-600 hover:bg-turquoise-700 text-white text-sm rounded-md transition-colors"
						>
							Accept All
						</button>
					</div>
				</div>
			{:else}
				<!-- Detailed Settings -->
				<div transition:fade={{ duration: 200 }}>
					<div class="flex items-center justify-between mb-4">
						<h3 class="text-white font-semibold text-lg">Cookie Preferences</h3>
						<button
							on:click={toggleDetails}
							class="text-white/70 hover:text-white"
							aria-label="Close cookie settings"
						>
							<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
							</svg>
						</button>
					</div>

					<div class="grid gap-4 mb-6">
						<!-- Necessary Cookies -->
						<div class="bg-slate-800/50 rounded-lg p-4">
							<div class="flex items-center justify-between mb-2">
								<h4 class="text-white font-medium">Necessary Cookies</h4>
								<div class="bg-green-600 text-white text-xs px-2 py-1 rounded">Always Active</div>
							</div>
							<p class="text-white/70 text-sm">
								Essential for the website to function properly. These cookies enable basic features like page navigation, 
								access to secure areas, and user authentication.
							</p>
						</div>

						<!-- Analytics Cookies -->
						<div class="bg-slate-800/50 rounded-lg p-4">
							<div class="flex items-center justify-between mb-2">
								<h4 class="text-white font-medium">Analytics Cookies</h4>
								<label class="relative inline-flex items-center cursor-pointer">
									<input 
										type="checkbox" 
										bind:checked={preferences.analytics}
										class="sr-only peer"
									>
									<div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-turquoise-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-turquoise-600"></div>
								</label>
							</div>
							<p class="text-white/70 text-sm">
								Help us understand how visitors interact with our website by collecting and reporting information anonymously. 
								Powered by Vercel Analytics.
							</p>
						</div>

						<!-- Functional Cookies -->
						<div class="bg-slate-800/50 rounded-lg p-4">
							<div class="flex items-center justify-between mb-2">
								<h4 class="text-white font-medium">Functional Cookies</h4>
								<label class="relative inline-flex items-center cursor-pointer">
									<input 
										type="checkbox" 
										bind:checked={preferences.functional}
										class="sr-only peer"
									>
									<div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-turquoise-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-turquoise-600"></div>
								</label>
							</div>
							<p class="text-white/70 text-sm">
								Enable enhanced functionality and personalization, such as remembering your preferences and 
								providing personalized content.
							</p>
						</div>
					</div>

					<div class="flex flex-col sm:flex-row gap-2 justify-end">
						<button
							on:click={acceptNecessary}
							class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded-md transition-colors"
						>
							Necessary Only
						</button>
						<button
							on:click={saveCustomPreferences}
							class="px-4 py-2 bg-turquoise-600 hover:bg-turquoise-700 text-white text-sm rounded-md transition-colors"
						>
							Save Preferences
						</button>
						<button
							on:click={acceptAll}
							class="px-4 py-2 bg-turquoise-600 hover:bg-turquoise-700 text-white text-sm rounded-md transition-colors"
						>
							Accept All
						</button>
					</div>
				</div>
			{/if}
		</div>
	</div>
{/if}

<!-- Cookie Settings Button (always visible for users to change preferences) -->
<button
	on:click={resetConsent}
	class="fixed bottom-4 left-4 z-40 bg-slate-800/80 hover:bg-slate-700/80 backdrop-blur-sm text-white/70 hover:text-white text-xs px-3 py-2 rounded-md transition-all duration-200 border border-white/10"
	title="Cookie Settings"
>
	🍪 Cookie Settings
</button>
