<script>
    import { fade, slide } from 'svelte/transition';
    import LoadingCard from './LoadingCard.svelte';
    import PosterPlaceholder from './PosterPlaceholder.svelte';

    /**
	 * @type {{title: string, description: string, year: number}}
	 */
    export let recommendation;

    /**
     * @type {string}
     */
    export let userCountry = 'US'; // Default to US if not provided

    let showCast = false;
    let showCrew = false;
    let showSeasons = false;
    let showProductionCompanies = false;
    let showProviders = false;

    let expandedSeasons = new Set();
    /**
	 * @param {any} idx
	 */
    function toggleSeasonOverview(idx) {
        if (expandedSeasons.has(idx)) {
            expandedSeasons.delete(idx);
        } else {
            expandedSeasons.add(idx);
        }
        // Force reactivity
        expandedSeasons = new Set(expandedSeasons);
    }

    let showFullLastEpOverview = false;
    function toggleLastEpOverview() {
        showFullLastEpOverview = !showFullLastEpOverview;
    }

    /**
	 * @param {string} text
	 */
    function truncate(text, len = 130) {
        if (!text) return '';
        return text.length > len ? text.slice(0, len) + '...' : text;
    }

    async function getDetailsFromTMDB() {
        const response = await fetch('/api/searchTvShows', {
            method: 'POST',
            body: JSON.stringify({
                title: recommendation.title,
                year: recommendation.year,
                userCountry: userCountry
            }),
            headers: {
                'content-type': 'application/json'
            }
        });
        let recommendationDetails = await response.json();
        if (recommendationDetails?.error) {
            return [];
        }
        return recommendationDetails;
    }

    let promise = getDetailsFromTMDB();
</script>

<div>
    {#await promise}
        <LoadingCard incomingStream={false} />
    {:then dataArr}
        {#if Array.isArray(dataArr.data) && dataArr.data.length > 0}
            {#each dataArr.data as data}
                {#if data.name && data.overview}
                    <div in:fade|global class="relative flex flex-col md:flex-row bg-neutral-800/70 shadow-md p-6">
                        <!-- Poster -->
                        {#if data.poster_path}
                            <!-- Desktop poster (side by side) -->
                            <div
                                class="hidden md:block h-[250px] flex-none w-1/5 bg-cover bg-center rounded-lg"
                                style={`background-image: url(https://image.tmdb.org/t/p/w185${data.poster_path});`}
                            />
                            <!-- Mobile poster (top of card) -->
                            <div
                                class="md:hidden w-full h-48 bg-cover bg-center rounded-lg mb-4"
                                style={`background-image: url(https://image.tmdb.org/t/p/w185${data.poster_path});`}
                            />
                        {:else}
                            <!-- Desktop placeholder -->
                            <div class="hidden md:block h-[250px] flex-none w-1/5">
                                <PosterPlaceholder />
                            </div>
                            <!-- Mobile placeholder -->
                            <div class="md:hidden w-full h-48 mb-4">
                                <PosterPlaceholder />
                            </div>
                        {/if}

                        <!-- Main Content -->
                        <div class="flex flex-col justify-between md:ml-6 w-full">
                            <div>
                                <!-- Title & Year -->
                                <div class="flex items-end mb-4 flex-wrap">
                                    <div class="font-bold text-slate-200 text-3xl">
                                        {data.name}
                                        <span class="font-bold text-slate-200/60 text-xl ml-2">{data.release_year}</span>
                                    </div>
                                    {#if data.status}
                                        <div title="Status" class="ml-4 mb-2 py-1 px-2 rounded-full text-turquoise-600 border border-turquoise-600 text-xs text-center">
                                            {data.status}
                                        </div>
                                    {/if}
                                </div>

                                <!-- Dates, Votes, Seasons, Episodes -->
                                <div class="flex items-center flex-wrap gap-2 mb-2">
                                    {#if data.first_air_date}
                                        <div title="First Air Date" class="py-1 px-2 rounded-full text-turquoise-600 border border-turquoise-600 text-xs text-center">
                                            {new Date(data.first_air_date).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}
                                        </div>
                                    {/if}
                                    {#if data.last_air_date}
                                        <div title="Last Air Date" class="py-1 px-2 rounded-full text-turquoise-600 border border-turquoise-600 text-xs text-center">
                                            {new Date(data.last_air_date).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}
                                        </div>
                                    {/if}
                                    <div title="TMDB Average Vote" class="py-1 px-2 rounded-full text-turquoise-600 border border-turquoise-600 text-xs text-center">
                                        {data.vote_average?.toFixed(2)}
                                    </div>
                                    {#if data.number_of_seasons}
                                        <div title="Seasons" class="py-1 px-2 rounded-full text-turquoise-600 border border-turquoise-600 text-xs text-center">
                                            {data.number_of_seasons} Seasons
                                        </div>
                                    {/if}
                                    {#if data.number_of_episodes}
                                        <div title="Episodes" class="py-1 px-2 rounded-full text-turquoise-600 border border-turquoise-600 text-xs text-center">
                                            {data.number_of_episodes} Episodes
                                        </div>
                                    {/if}
                                </div>

                                <!-- Genres -->
                                {#if data.genres && data.genres.length > 0}
                                    <div class="flex flex-wrap gap-2 mb-2">
                                        {#each data.genres as genre}
                                            <div title="Genre {genre.name}" class="py-1 px-2 rounded-full text-turquoise-600 border border-turquoise-600 text-xs text-center">
                                                {genre.name}
                                            </div>
                                        {/each}
                                    </div>
                                {/if}

                                <!-- Tagline & Overview -->
                                <div class="text-slate-200/90 mb-4">
                                    {#if data.tagline}
                                        <div class="font-bold text-slate-200 text-2xl mb-2 italic">{data.tagline}</div>
                                    {/if}
                                    {data.overview}
                                </div>

                                <!-- Homepage -->
                                {#if data.homepage}
                                    <div class="text-slate-200/90 mb-2">
                                        <b>Homepage:</b> <a href="{data.homepage}" target="_blank" class="text-turquoise-400 underline">{data.homepage}</a>
                                    </div>
                                {/if}

                                <!-- Networks -->
                                {#if data.networks && data.networks.length > 0}
                                    <div class="mb-4">
                                        <h3 class="text-slate-200 text-lg font-bold mb-2">Networks</h3>
                                        <div class="flex flex-wrap gap-4">
                                            {#each data.networks as network}
                                                <div class="flex flex-col items-center w-24">
                                                    {#if network.logo_path}
                                                        <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden p-2">
                                                            <img
                                                                src={`https://image.tmdb.org/t/p/w45/${network.logo_path}`}
                                                                alt="{network.name} Logo"
                                                                class="w-full h-full object-contain"
                                                                on:error={(e) => {
                                                                    if (e.target instanceof HTMLImageElement) {
                                                                        e.target.src = '/movie-camera.png';
                                                                    }
                                                                }}
                                                            />
                                                        </div>
                                                    {:else}
                                                        <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden mb-2">
                                                            <img
                                                                src="/movie-camera.png"
                                                                alt="{network.name}"
                                                                class="w-full h-full object-cover"
                                                            />
                                                        </div>
                                                    {/if}
                                                    <div class="text-slate-200 text-sm font-bold text-center mt-2">{network.name}</div>
                                                </div>
                                            {/each}
                                        </div>
                                    </div>
                                {/if}

                                <!-- Trailers -->
                                {#if data.videos && data.videos.results && data.videos.results.length > 0}
                                    <div class="text-slate-200/50 mb-4">
                                        <div class="mt-6">
                                            <h3 class="text-slate-200 text-xl font-bold mb-4">Trailers</h3>
                                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                                {#each data.videos.results.filter((/** @type {{ type: string; }} */ v) => v.type === 'Trailer') as trailer}
                                                    <div class="flex flex-col items-center">
                                                        <div class="w-full aspect-video rounded-lg overflow-hidden bg-black mb-2">
                                                            {#if trailer.site === 'YouTube' && trailer.key}
                                                                <iframe
                                                                    class="w-full h-full"
                                                                    src={`https://www.youtube.com/embed/${trailer.key}`}
                                                                    title={trailer.name}
                                                                    frameborder="0"
                                                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                                                    allowfullscreen
                                                                ></iframe>
                                                            {:else if trailer.site === 'Vimeo' && trailer.key}
                                                                <iframe
                                                                    class="w-full h-full"
                                                                    src={`https://player.vimeo.com/video/${trailer.key}`}
                                                                    title={trailer.name}
                                                                    frameborder="0"
                                                                    allow="autoplay; fullscreen; picture-in-picture"
                                                                    allowfullscreen
                                                                ></iframe>
                                                            {:else}
                                                                <a href={trailer.url} target="_blank" class="text-turquoise-400 underline">Watch trailer</a>
                                                            {/if}
                                                        </div>
                                                        <div class="text-slate-200 text-sm font-bold text-center">{trailer.name}</div>
                                                        <div class="text-slate-400 text-xs text-center">{trailer.site}</div>
                                                    </div>
                                                {/each}
                                            </div>
                                        </div>
                                    </div>
                                {/if}

                                <!-- Last Episode Aired -->
                                {#if data.last_episode_to_air && data.last_episode_to_air.name}
                                    <div class="text-slate-200/90 mb-6">
                                        <h3 class="text-slate-200 text-xl font-bold mb-4">Last Episode Aired</h3>
                                        <div class="flex flex-col md:flex-row gap-4 items-start">
                                            {#if data.last_episode_to_air.still_path}
                                                <img
                                                    src={`https://image.tmdb.org/t/p/w300${data.last_episode_to_air.still_path}`}
                                                    alt={data.last_episode_to_air.name}
                                                    class="rounded-lg shadow-md w-full md:w-64 object-cover"
                                                    style="max-height:180px"
                                                />
                                            {/if}
                                            <div class="flex-1">
                                                <div class="font-bold text-lg mb-1">{data.last_episode_to_air.name}</div>
                                                <div class="text-slate-400 text-xs mb-1">
                                                    S{data.last_episode_to_air.season_number}E{data.last_episode_to_air.episode_number}
                                                    &middot;
                                                    {new Date(data.last_episode_to_air.air_date).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}
                                                    {#if data.last_episode_to_air.vote_average}
                                                        &middot; ⭐ {data.last_episode_to_air.vote_average}
                                                    {/if}
                                                </div>
                                                <div class="text-slate-200/80 text-sm mb-1">
                                                    {#if data.last_episode_to_air.overview && data.last_episode_to_air.overview.length > 130}
                                                        {#if showFullLastEpOverview}
                                                            {data.last_episode_to_air.overview}
                                                            <button class="ml-2 text-turquoise-400 underline" on:click={toggleLastEpOverview}>less</button>
                                                        {:else}
                                                            {truncate(data.last_episode_to_air.overview, 130)}
                                                            <button class="ml-2 text-turquoise-400 underline" on:click={toggleLastEpOverview}>more</button>
                                                        {/if}
                                                    {:else}
                                                        {data.last_episode_to_air.overview}
                                                    {/if}
                                                </div>
                                                {#if data.last_episode_to_air.runtime}
                                                    <div class="text-slate-400 text-xs">Runtime: {data.last_episode_to_air.runtime} min</div>
                                                {/if}
                                            </div>
                                        </div>
                                    </div>
                                {/if}

                                {#if data.seasons && data.seasons.length > 0}
                                    <div class="text-slate-200/50 mb-4">
                                        <div class="mt-6">
                                            <button
                                                class="flex items-center gap-2 text-slate-200 text-xl font-bold mb-4 focus:outline-none"
                                                on:click={() => showSeasons = !showSeasons}
                                                aria-expanded={showSeasons}
                                            >
                                                <span>Seasons</span>
                                                <svg class="w-5 h-5 transition-transform" style="transform: rotate({showSeasons ? 90 : 0}deg);" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                                </svg>
                                            </button>
                                            {#if showSeasons}
                                                <div transition:slide>
                                                    <div class="flex flex-wrap gap-6">
                                                        {#each data.seasons as season, index}
                                                            <div class="flex flex-col items-center w-40 bg-neutral-900/70 rounded-lg p-3 shadow">
                                                                {#if season.poster_path}
                                                                    <img
                                                                        src={`https://image.tmdb.org/t/p/w185${season.poster_path}`}
                                                                        alt={season.name}
                                                                        class="rounded-lg mb-2 w-full object-cover"
                                                                        style="max-height:180px"
                                                                        on:error={(e) => {
                                                                            if (e.target instanceof HTMLImageElement) {
                                                                                e.target.src = '/movie-camera.png';
                                                                            }
                                                                        }}
                                                                    />
                                                                {:else}
                                                                    <div class="w-full h-[180px] flex items-center justify-center bg-neutral-700 rounded-lg mb-2">
                                                                        <img src="/movie-camera.png" alt="No poster" class="w-12 h-12 opacity-60" />
                                                                    </div>
                                                                {/if}
                                                                <div class="font-bold text-slate-200 text-center">{season.name}</div>
                                                                <div class="text-slate-400 text-xs text-center mb-1">
                                                                    {season.air_date ? new Date(season.air_date).getFullYear() : ''} 
                                                                    {season.episode_count ? ` • ${season.episode_count} episodes` : ''}
                                                                </div>
                                                                {#if season.vote_average}
                                                                    <div class="text-xs text-turquoise-400 mb-1">⭐ {season.vote_average}</div>
                                                                {/if}
                                                                {#if season.overview}
                                                                    <div class="text-slate-200/80 text-xs mt-1">
                                                                        {#if season.overview.length > 130}
                                                                            {#if expandedSeasons.has(index)}
                                                                                {season.overview}
                                                                                <button class="ml-2 text-turquoise-400 underline" on:click={() => toggleSeasonOverview(index)}>
                                                                                    less
                                                                                </button>
                                                                            {:else}
                                                                                {truncate(season.overview)}
                                                                                <button class="ml-2 text-turquoise-400 underline" on:click={() => toggleSeasonOverview(index)}>
                                                                                    more
                                                                                </button>
                                                                            {/if}
                                                                        {:else}
                                                                            {season.overview}
                                                                        {/if}
                                                                    </div>
                                                                {/if}
                                                            </div>
                                                        {/each}
                                                    </div>
                                                </div>
                                            {/if}
                                        </div>
                                    </div>
                                {/if}

                                <!-- Production Companies -->
                                {#if data.production_companies && data.production_companies.length > 0}
                                    <div class="text-black-200/50 mb-4">
                                        <div class="mt-6">
                                            <button
                                                class="flex items-center gap-2 text-slate-200 text-xl font-bold mb-4 focus:outline-none"
                                                on:click={() => showProductionCompanies = !showProductionCompanies}
                                                aria-expanded={showProductionCompanies}
                                            >
                                                <span>Production Companies</span>
                                                <svg class="w-5 h-5 transition-transform" style="transform: rotate({showProductionCompanies ? 90 : 0}deg);" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                                </svg>
                                            </button>
                                            {#if showProductionCompanies}
                                                <div transition:slide>
                                                    <div class="flex flex-wrap gap-6">
                                                        {#each data.production_companies as company}
                                                            <div class="flex flex-col items-center w-24">
                                                                {#if company.logo_path}
                                                                    <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden p-2">
                                                                        <img
                                                                            src={`https://image.tmdb.org/t/p/w45/${company.logo_path}`}
                                                                            alt="{company.name} Logo"
                                                                            class="w-full h-full object-contain"
                                                                            on:error={(e) => {
                                                                                if (e.target instanceof HTMLImageElement) {
                                                                                    e.target.src = '/movie-camera.png';
                                                                                }
                                                                            }}
                                                                        />
                                                                    </div>
                                                                {:else}
                                                                    <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden mb-2">
                                                                        <img
                                                                            src="/movie-camera.png"
                                                                            alt="{company.name}"
                                                                            class="w-full h-full object-cover"
                                                                        />
                                                                    </div>
                                                                {/if}
                                                                <div class="text-slate-200 text-sm font-bold text-center mt-2">{company.name}</div>
                                                            </div>
                                                        {/each}
                                                    </div>
                                                </div>
                                            {/if}
                                        </div>
                                    </div>
                                {/if}

                                <!-- Streaming Providers -->
                                {#if data.providers && data.providers.results && data.providers.results[userCountry]}
                                    <div class="text-slate-200/50 mb-4">
                                        <div class="mt-6">
                                            <div class="flex items-center justify-between mb-4">
                                                <button
                                                    class="flex items-center gap-2 text-slate-200 text-xl font-bold focus:outline-none"
                                                    on:click={() => showProviders = !showProviders}
                                                    aria-expanded={showProviders}
                                                >
                                                    <span>Where to Watch</span>
                                                    <svg class="w-5 h-5 transition-transform" style="transform: rotate({showProviders ? 90 : 0}deg);" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                                    </svg>
                                                </button>
                                                <img src="/justwatch.webp" alt="JustWatch" class="h-3 opacity-50" />
                                            </div>
                                            {#if showProviders}
                                                <div transition:slide>
                                                    {#if data.providers && data.providers.results && data.providers.results[userCountry]}
                                                        {@const countryData = data.providers.results[userCountry]}
                                                        <div class="flex flex-col gap-6">
                                                            {#if countryData.flatrate && countryData.flatrate.length > 0}
                                                                <div>
                                                                    <h4 class="text-slate-200 text-lg font-bold mb-3">Streaming</h4>
                                                                    <div class="flex flex-wrap gap-4">
                                                                        {#each countryData.flatrate as provider}
                                                                            <a href={countryData.link} target="_blank" class="flex flex-col items-center w-24">
                                                                                <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden p-2">
                                                                                    <img
                                                                                        src={`https://image.tmdb.org/t/p/w45/${provider.logo_path}`}
                                                                                        alt="{provider.provider_name} Logo"
                                                                                        class="w-full h-full object-contain"
                                                                                        on:error={(e) => {
                                                                                            if (e.target instanceof HTMLImageElement) {
                                                                                                e.target.src = '/movie-camera.png';
                                                                                            }
                                                                                        }}
                                                                                    />
                                                                                </div>
                                                                                <div class="text-slate-200 text-sm font-bold text-center mt-2">{provider.provider_name}</div>
                                                                            </a>
                                                                        {/each}
                                                                    </div>
                                                                </div>
                                                            {/if}
                                                            {#if countryData.rent && countryData.rent.length > 0}
                                                                <div>
                                                                    <h4 class="text-slate-200 text-lg font-bold mb-3">Rent</h4>
                                                                    <div class="flex flex-wrap gap-4">
                                                                        {#each countryData.rent as provider}
                                                                            <a href={countryData.link} target="_blank" class="flex flex-col items-center w-24">
                                                                                <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden p-2">
                                                                                    <img
                                                                                        src={`https://image.tmdb.org/t/p/w45/${provider.logo_path}`}
                                                                                        alt="{provider.provider_name} Logo"
                                                                                        class="w-full h-full object-contain"
                                                                                        on:error={(e) => {
                                                                                            if (e.target instanceof HTMLImageElement) {
                                                                                                e.target.src = '/movie-camera.png';
                                                                                            }
                                                                                        }}
                                                                                    />
                                                                                </div>
                                                                                <div class="text-slate-200 text-sm font-bold text-center mt-2">{provider.provider_name}</div>
                                                                            </a>
                                                                        {/each}
                                                                    </div>
                                                                </div>
                                                            {/if}
                                                            {#if countryData.buy && countryData.buy.length > 0}
                                                                <div>
                                                                    <h4 class="text-slate-200 text-lg font-bold mb-3">Buy</h4>
                                                                    <div class="flex flex-wrap gap-4">
                                                                        {#each countryData.buy as provider}
                                                                            <a href={countryData.link} target="_blank" class="flex flex-col items-center w-24">
                                                                                <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden p-2">
                                                                                    <img
                                                                                        src={`https://image.tmdb.org/t/p/w45/${provider.logo_path}`}
                                                                                        alt="{provider.provider_name} Logo"
                                                                                        class="w-full h-full object-contain"
                                                                                        on:error={(e) => {
                                                                                            if (e.target instanceof HTMLImageElement) {
                                                                                                e.target.src = '/movie-camera.png';
                                                                                            }
                                                                                        }}
                                                                                    />
                                                                                </div>
                                                                                <div class="text-slate-200 text-sm font-bold text-center mt-2">{provider.provider_name}</div>
                                                                            </a>
                                                                        {/each}
                                                                    </div>
                                                                </div>
                                                            {/if}
                                                        </div>
                                                    {/if}
                                                </div>
                                            {/if}
                                        </div>
                                    </div>
                                {/if}

                                <!-- Casts Accordion -->
                                {#if data.credits && data.credits.cast && data.credits.cast.length > 0}
                                    <div class="text-slate-200/50 mb-4">
                                        <div class="mt-6">
                                            <button
                                                class="flex items-center gap-2 text-slate-200 text-xl font-bold mb-4 focus:outline-none"
                                                on:click={() => showCast = !showCast}
                                                aria-expanded={showCast}
                                            >
                                                <span>Casts</span>
                                                <svg class="w-5 h-5 transition-transform" style="transform: rotate({showCast ? 90 : 0}deg);" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                                </svg>
                                            </button>
                                            {#if showCast}
                                                <div transition:slide>
                                                    <div class="flex flex-wrap gap-4">
                                                        {#each data.credits.cast.slice(0, 12) as cast}
                                                            <a href={`https://www.themoviedb.org/search?query=${cast.name}`} target="_blank">
                                                                <div class="flex flex-col items-center w-24">
                                                                    {#if cast.profile_path}
                                                                        <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden mb-2">
                                                                            <img
                                                                                src={`https://image.tmdb.org/t/p/w45/${cast.profile_path}`}
                                                                                alt={cast.name}
                                                                                class="w-full h-full object-cover"
                                                                                on:error={(e) => {
                                                                                    if (e.target instanceof HTMLImageElement) {
                                                                                        e.target.src = '/default-people.png';
                                                                                    }
                                                                                }}
                                                                            />
                                                                        </div>
                                                                    {:else}
                                                                        <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden mb-2">
                                                                            <img
                                                                                src="/default-people.png"
                                                                                alt={cast.name}
                                                                                class="w-full h-full object-cover"
                                                                            />
                                                                        </div>
                                                                    {/if}
                                                                    <div class="text-slate-200 text-sm font-bold text-center">{cast.name}</div>
                                                                    <div class="text-slate-200/70 text-xs text-center">{cast.character}</div>
                                                                </div>
                                                            </a>
                                                        {/each}
                                                    </div>
                                                </div>
                                            {/if}
                                        </div>
                                    </div>
                                {/if}

                                <!-- Crews Accordion -->
                                {#if data.credits && data.credits.crew && data.credits.crew.length > 0}
                                    <div class="text-slate-200/50 mb-4">
                                        <div class="mt-6">
                                            <button
                                                class="flex items-center gap-2 text-slate-200 text-xl font-bold mb-4 focus:outline-none"
                                                on:click={() => showCrew = !showCrew}
                                                aria-expanded={showCrew}
                                            >
                                                <span>Crews</span>
                                                <svg class="w-5 h-5 transition-transform" style="transform: rotate({showCrew ? 90 : 0}deg);" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                                </svg>
                                            </button>
                                            {#if showCrew}
                                                <div transition:slide>
                                                    <div class="flex flex-wrap gap-4">
                                                        {#each data.credits.crew.slice(0, 12) as crew}
                                                            <a href={`https://www.themoviedb.org/search?query=${crew.name}`} target="_blank">
                                                                <div class="flex flex-col items-center w-24">
                                                                    {#if crew.profile_path}
                                                                        <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden mb-2">
                                                                            <img
                                                                                src={`https://image.tmdb.org/t/p/w45/${crew.profile_path}`}
                                                                                alt={crew.name}
                                                                                class="w-full h-full object-cover"
                                                                                on:error={(e) => {
                                                                                    if (e.target instanceof HTMLImageElement) {
                                                                                        e.target.src = '/default-people.png';
                                                                                    }
                                                                                }}
                                                                            />
                                                                        </div>
                                                                    {:else}
                                                                        <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden mb-2">
                                                                            <img
                                                                                src="/default-people.png"
                                                                                alt={crew.name}
                                                                                class="w-full h-full object-cover"
                                                                            />
                                                                        </div>
                                                                    {/if}
                                                                    <div class="text-slate-200 text-sm font-bold text-center">{crew.name}</div>
                                                                    <div class="text-slate-200/70 text-xs text-center">{crew.job}</div>
                                                                </div>
                                                            </a>
                                                        {/each}
                                                    </div>
                                                </div>
                                            {/if}
                                        </div>
                                    </div>
                                {/if}


                                
                                
                            </div>
                        </div>
                    </div>
                {/if}
            {/each}
        {/if}
    {:catch error}
        <!-- What to do when some error? -->
    {/await}
</div>
