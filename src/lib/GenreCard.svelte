<script>
    import { slide } from 'svelte/transition';

    /**
     * @type {string[]}
     * The list of all available categories/genres.
     */
    export let categoryTypes = [];

    /**
     * @type {string[]}
     * The currently selected categories.
     */
    export let selectedCategories = [];

    let showAll = false;
    const itemsPerRow = 6;

    function toggleShowAll() {
        showAll = !showAll;
    }
</script>

<div>
    <div class="mb-4 font-semibold text-2xl">
        Or if you prefer the familiar favorites, browse our traditional genre lineup.
    </div>
    <!-- Always show the first row -->
    <div class="flex items-center flex-wrap">
        {#each categoryTypes.slice(0, itemsPerRow) as category}
            <label
                class={`${
                    selectedCategories.includes(category) ? 'bg-turquoise-600/40' : ''
                } text-slate-200 font-bold mr-2 mt-2 text-sm py-2 px-4 rounded-full border border-turquoise-600`}
            >
                <input
                    class="hidden"
                    type="radio"
                    bind:group={selectedCategories}
                    name="categories"
                    value={category}
                />
                {category}
            </label>
        {/each}
    </div>

    {#if categoryTypes.length > itemsPerRow}
        {#if showAll}
            <div transition:slide>
                <div class="flex items-center flex-wrap mt-2">
                    {#each categoryTypes.slice(itemsPerRow) as category}
                        <label
                            class={`${
                                selectedCategories.includes(category) ? 'bg-turquoise-600/40' : ''
                            } text-slate-200 font-bold mr-2 mt-2 text-sm py-2 px-4 rounded-full border border-turquoise-600`}
                        >
                            <input
                                class="hidden"
                                type="radio"
                                bind:group={selectedCategories}
                                name="categories"
                                value={category}
                            />
                            {category}
                        </label>
                    {/each}
                </div>
                <div class="mt-2 flex justify-center">
                    <button
                        class="text-turquoise-400 hover:underline focus:outline-none transition-all"
                        on:click={toggleShowAll}
                        aria-expanded={showAll}
                    >
                        Show less
                    </button>
                </div>
            </div>
        {:else}
            <div class="mt-2 flex justify-center">
                <button
                    class="text-turquoise-400 hover:underline focus:outline-none transition-all"
                    on:click={toggleShowAll}
                    aria-expanded={showAll}
                >
                    Show more
                </button>
            </div>
        {/if}
    {/if}
</div>