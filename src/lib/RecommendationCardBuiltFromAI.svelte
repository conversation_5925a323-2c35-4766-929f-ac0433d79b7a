<script>
    import { fade } from 'svelte/transition';
    import PosterPlaceholder from './PosterPlaceholder.svelte';

    export let title;
    export let description;
    export let year;
</script>
<div in:fade|global class="relative flex flex-col md:flex-row bg-neutral-800/70 shadow-md p-6">
    <div class="text-white/50 flex items-center justify-center h-[250px] flex-none w-1/5 bg-neutral-900">
        <PosterPlaceholder />
    </div>
    <div class="md:hidden z-10 absolute inset-0 bg-cover bg-center">
        <div class="w-full h-full bg-black/80 bg-blur-sm" />
    </div>

    <div class="z-40 flex flex-col justify-between md:ml-6">
        <div>
            <div class="flex items-end mb-4">
                <div title="Movie title" class="font-bold text-slate-200 text-3xl">
                    {title}
                    <span title="Release year" class="font-bold text-slate-200/60 text-xl ml-2">{year}</span>
                </div>
            </div>
            <div title="Movie short plot/synopsis" class="text-slate-200/90 mb-4">
                {description}
            </div>
        </div>
    </div>
</div>