<script lang="ts">
	import TvIcon from './TvIcon.svelte';
	import { createEventDispatcher } from 'svelte';
	import { goto } from '$app/navigation';

	interface UserData {
		email: string;
		country_code: string;
		isAdmin?: boolean;
		userType?: string;
	}

	export let isLoggedIn: boolean = false;
	export let userData: UserData | null = null;

	const dispatch = createEventDispatcher();

	function handleLogout() {
		dispatch('logout');
	}

	function handleLogoClick() {
		if (isLoggedIn) {
			goto('/recommendations');
		} else {
			goto('/');
		}
	}
</script>

<div
	class="max-w-4xl mx-auto w-full flex md:flex-row flex-col items-center md:justify-between py-8 mb-12 border-b border-white/20"
>
	<button on:click={handleLogoClick} class="flex items-center mb-4 md:mb-0 text-white/70 hover:text-white transition-colors cursor-pointer">
		<TvIcon />
		<div class="text-2xl md:text-xl font-bold ml-2">Cinemated</div>
	</button>

	{#if isLoggedIn && userData}
		<div class="flex items-center gap-4">
			<div class="text-white/70 text-sm">
				{userData.email} ({userData.country_code})
			</div>
			{#if userData.isAdmin}
				<a
					href="/admin"
					class="bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-md px-2 py-2 text-sm"
				>
					Admin Panel
				</a>
			{/if}
			<button
				on:click={handleLogout}
				class="bg-red-600 hover:bg-red-700 text-white font-medium rounded-md px-2 py-2 text-sm"
			>
				Logout
			</button>
		</div>
	{/if}
</div>
