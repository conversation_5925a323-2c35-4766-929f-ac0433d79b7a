<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { fade, scale } from 'svelte/transition';

	export let isOpen: boolean = false;
	export let title: string = '';
	export let maxWidth: string = 'max-w-md';

	const dispatch = createEventDispatcher();

	function closeModal() {
		isOpen = false;
		dispatch('close');
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			closeModal();
		}
	}

	function handleBackdropClick(event: MouseEvent) {
		if (event.target === event.currentTarget) {
			closeModal();
		}
	}

	function handleBackdropKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			closeModal();
		}
	}
</script>

<svelte:window on:keydown={handleKeydown} />

{#if isOpen}
	<!-- Modal backdrop -->
	<div
		class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
		on:click={handleBackdropClick}
		on:keydown={handleBackdropKeydown}
		role="button"
		tabindex="0"
		aria-label="Close modal"
		transition:fade={{ duration: 200 }}
	>
		<!-- Modal content -->
		<div
			class="bg-slate-800 rounded-lg shadow-xl {maxWidth} w-full max-h-[90vh] overflow-y-auto"
			transition:scale={{ duration: 200, start: 0.95 }}
		>
			<!-- Modal header -->
			{#if title}
				<div class="flex items-center justify-between p-6 border-b border-white/10">
					<h3 class="text-xl font-semibold text-white">{title}</h3>
					<button
						on:click={closeModal}
						class="text-white/70 hover:text-white transition-colors"
					>
						<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
						</svg>
					</button>
				</div>
			{/if}

			<!-- Modal body -->
			<div class="p-6">
				<slot />
			</div>

			<!-- Modal footer (if slot is provided) -->
			<div class="px-6 pb-6">
				<slot name="footer" />
			</div>
		</div>
	</div>
{/if}
