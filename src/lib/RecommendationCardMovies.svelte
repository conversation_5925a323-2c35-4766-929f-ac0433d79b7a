<script>
    import { fade, slide } from 'svelte/transition';
    import LoadingCard from './LoadingCard.svelte';
    import PosterPlaceholder from './PosterPlaceholder.svelte';

    /**
     * @type {{title: string, description: string, year: number}}
     */
    export let recommendation;

    /**
     * @type {string}
     */
    export let userCountry = 'US'; // Default to US if not provided

    let showCast = false;
    let showCrew = false;
    let showProductionCompanies = false;
    let showFullOverview = false;
    let showVideos = false;
    let showProviders = false;

    function toggleOverview() {
        showFullOverview = !showFullOverview;
    }

    /**
	 * @param {string} text
	 */
    function truncate(text, len = 160) {
        if (!text) return '';
        return text.length > len ? text.slice(0, len) + '...' : text;
    }

    async function getDetailsFromTMDB() {
        const response = await fetch('/api/searchMovies', {
            method: 'POST',
            body: JSON.stringify({
                title: recommendation.title,
                year: recommendation.year,
                userCountry: userCountry
            }),
            headers: {
                'content-type': 'application/json'
            }
        });

        let recommendationDetails = await response.json();

        if (recommendationDetails?.error) {
            return [];
        }

        return recommendationDetails;
    }

    let promise = getDetailsFromTMDB();

    // Helper for YouTube/Vimeo embed
    /**
	 * @param {string} url
	 */
    function getYoutubeId(url) {
        const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([A-Za-z0-9_-]+)/);
        return match ? match[1] : null;
    }
    /**
	 * @param {string} url
	 */
    function getVimeoId(url) {
        const match = url.match(/vimeo\.com\/(\d+)/);
        return match ? match[1] : null;
    }
</script>

<div>
    {#await promise}
        <LoadingCard incomingStream={false} />
    {:then dataArr}
        {#if Array.isArray(dataArr.data) && dataArr.data.length > 0}
            {#each dataArr.data as data}
                {#if data.title && data.overview}
                    <div in:fade|global class="relative flex flex-col md:flex-row bg-neutral-800/70 shadow-md p-6">
                        <!-- Poster -->
                        {#if data.poster_path}
                            <div
                                class="hidden md:block h-[250px] flex-none w-1/5 bg-cover bg-center rounded-lg"
                                style={`background-image: url(https://image.tmdb.org/t/p/w185${data.poster_path}); z-index: 12;`}
                            />
                            <div
                                class="md:hidden z-10 absolute inset-0 bg-cover bg-center"
                                style={`background-image: url(https://image.tmdb.org/t/p/w185${data.poster_path}); display: block;`}
                            >
                                <div class="w-full h-full bg-black/80 bg-blur-sm" />
                            </div>
                        {:else}
                            <PosterPlaceholder />
                        {/if}

                        <!-- Main Content -->
                        <div class="z-40 flex flex-col justify-between md:ml-6 pt-32 md:pt-0 w-full">
                            <div>
                                <!-- Title & Year -->
                                <div class="flex items-end mb-4 flex-wrap">
                                    <div class="font-bold text-slate-200 text-3xl">
                                        {data.title}
                                        <span class="font-bold text-slate-200/60 text-xl ml-2">{data.release_year}</span>
                                    </div>
                                    {#if data.status}
                                        <div title="Status" class="ml-4 mb-2 py-1 px-2 rounded-full text-turquoise-600 border border-turquoise-600 text-xs text-center">
                                            {data.status}
                                        </div>
                                    {/if}
                                </div>

                                <!-- Dates, Votes, Runtime, Budget, Revenue -->
                                <div class="flex items-center flex-wrap gap-2 mb-2">
                                    {#if data.release_date}
                                        <div title="Release Date" class="py-1 px-2 rounded-full text-turquoise-600 border border-turquoise-600 text-xs text-center">
                                            {new Date(data.release_date).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}
                                        </div>
                                    {/if}
                                    {#if data.runtime}
                                        <div title="Runtime" class="py-1 px-2 rounded-full text-turquoise-600 border border-turquoise-600 text-xs text-center">
                                            {data.runtime} mins
                                        </div>
                                    {/if}
                                    <div title="TMDB Average Vote" class="py-1 px-2 rounded-full text-turquoise-600 border border-turquoise-600 text-xs text-center">
                                        {data.vote_average?.toFixed(2)}
                                    </div>
                                    {#if data.budget && data.budget > 0}
                                        <div title="Budget" class="py-1 px-2 rounded-full text-turquoise-600 border border-turquoise-600 text-xs text-center">
                                            {data.budget >= 1000000
                                                ? `$${(data.budget / 1000000).toFixed(1)}M`
                                                : data.budget >= 1000
                                                ? `$${(data.budget / 1000).toFixed(1)}K`
                                                : `$${data.budget}`}
                                        </div>
                                    {/if}
                                    {#if data.revenue && data.revenue > 0}
                                        <div title="Revenue" class="py-1 px-2 rounded-full text-turquoise-600 border border-turquoise-600 text-xs text-center">
                                            {data.revenue >= 1000000
                                                ? `$${(data.revenue / 1000000).toFixed(1)}M`
                                                : data.revenue >= 1000
                                                ? `$${(data.revenue / 1000).toFixed(1)}K`
                                                : `$${data.revenue}`}
                                        </div>
                                    {/if}
                                </div>

                                <!-- Genres -->
                                {#if data.genres && data.genres.length > 0}
                                    <div class="flex flex-wrap gap-2 mb-2">
                                        {#each data.genres as genre}
                                            <div title="Genre {genre.name}" class="py-1 px-2 rounded-full text-turquoise-600 border border-turquoise-600 text-xs text-center">
                                                {genre.name}
                                            </div>
                                        {/each}
                                    </div>
                                {/if}

                                <!-- Tagline & Overview -->
                                <div class="text-slate-200/90 mb-4">
                                    {#if data.tagline}
                                        <div class="font-bold text-slate-200 text-2xl mb-2 italic">{data.tagline}</div>
                                    {/if}
                                    {#if data.overview.length > 200}
                                        {#if showFullOverview}
                                            {data.overview}
                                            <button class="ml-2 text-turquoise-400 underline" on:click={toggleOverview}>less</button>
                                        {:else}
                                            {truncate(data.overview, 200)}
                                            <button class="ml-2 text-turquoise-400 underline" on:click={toggleOverview}>more</button>
                                        {/if}
                                    {:else}
                                        {data.overview}
                                    {/if}
                                </div>

                                <!-- Homepage -->
                                {#if data.homepage}
                                    <div class="text-slate-200/90 mb-2">
                                        <b>Homepage:</b> <a href={data.homepage} target="_blank" class="text-turquoise-400 underline">{data.homepage}</a>
                                    </div>
                                {/if}

                                <!-- IMDB -->
                                {#if data.imdb_id}
                                    <div class="text-slate-200/90 mb-2">
                                        <b>IMDB:</b> <a href={`https://www.imdb.com/title/${data.imdb_id}/`} target="_blank" class="text-turquoise-400 underline">https://www.imdb.com/title/{data.imdb_id}/</a>
                                    </div>
                                {/if}

                                <!-- Trailers -->
                                {#if data.trailers && data.trailers.length > 0}
                                    <div class="text-slate-200/50 mb-4">
                                        <div class="mt-6">
                                            <h3 class="text-slate-200 text-xl font-bold mb-4">Trailers</h3>
                                            <div class="flex flex-wrap gap-6">
                                                {#each data.trailers as trailer}
                                                    <div class="flex flex-col items-center w-80">
                                                        <div class="w-full aspect-video rounded-lg overflow-hidden bg-black mb-2">
                                                            {#if trailer.site === 'YouTube' && getYoutubeId(trailer.url)}
                                                                <iframe
                                                                    class="w-full h-full"
                                                                    src={`https://www.youtube.com/embed/${getYoutubeId(trailer.url)}`}
                                                                    title={trailer.name}
                                                                    frameborder="0"
                                                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                                                    allowfullscreen
                                                                ></iframe>
                                                            {:else if trailer.site === 'Vimeo' && getVimeoId(trailer.url)}
                                                                <iframe
                                                                    class="w-full h-full"
                                                                    src={`https://player.vimeo.com/video/${getVimeoId(trailer.url)}`}
                                                                    title={trailer.name}
                                                                    frameborder="0"
                                                                    allow="autoplay; fullscreen; picture-in-picture"
                                                                    allowfullscreen
                                                                ></iframe>
                                                            {:else}
                                                                <a href={trailer.url} target="_blank" class="text-turquoise-400 underline">Watch trailer</a>
                                                            {/if}
                                                        </div>
                                                        <div class="text-slate-200 text-sm font-bold text-center">{trailer.name}</div>
                                                        <div class="text-slate-400 text-xs text-center">{trailer.site}</div>
                                                    </div>
                                                {/each}
                                            </div>
                                        </div>
                                    </div>
                                {/if}

                                <!-- Casts Accordion -->
                                {#if data.casts && data.casts.length > 0}
                                    <div class="text-slate-200/50 mb-4">
                                        <div class="mt-6">
                                            <button
                                                class="flex items-center gap-2 text-slate-200 text-xl font-bold mb-4 focus:outline-none"
                                                on:click={() => showCast = !showCast}
                                                aria-expanded={showCast}
                                            >
                                                <span>Casts</span>
                                                <svg class="w-5 h-5 transition-transform" style="transform: rotate({showCast ? 90 : 0}deg);" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                                </svg>
                                            </button>
                                            {#if showCast}
                                                <div transition:slide>
                                                    <div class="flex flex-wrap gap-4">
                                                        {#each data.casts.slice(0, 12) as cast}
                                                            <a href={`https://www.themoviedb.org/search?query=${cast.name}`} target="_blank">
                                                                <div class="flex flex-col items-center w-24">
                                                                    {#if cast.profile_path}
                                                                        <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden mb-2">
                                                                            <img
                                                                                src={`https://image.tmdb.org/t/p/w45/${cast.profile_path}`}
                                                                                alt={cast.name}
                                                                                class="w-full h-full object-cover"
                                                                                on:error={(e) => {
                                                                                    if (e.target instanceof HTMLImageElement) {
                                                                                        e.target.src = '/default-people.png';
                                                                                    }
                                                                                }}
                                                                            />
                                                                        </div>
                                                                    {:else}
                                                                        <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden mb-2">
                                                                            <img
                                                                                src="/default-people.png"
                                                                                alt={cast.name}
                                                                                class="w-full h-full object-cover"
                                                                            />
                                                                        </div>
                                                                    {/if}
                                                                    <div class="text-slate-200 text-sm font-bold text-center">{cast.name}</div>
                                                                    <div class="text-slate-200/70 text-xs text-center">{cast.character}</div>
                                                                </div>
                                                            </a>
                                                        {/each}
                                                    </div>
                                                </div>
                                            {/if}
                                        </div>
                                    </div>
                                {/if}

                                <!-- Crews Accordion -->
                                {#if data.crews && data.crews.length > 0}
                                    <div class="text-slate-200/50 mb-4">
                                        <div class="mt-6">
                                            <button
                                                class="flex items-center gap-2 text-slate-200 text-xl font-bold mb-4 focus:outline-none"
                                                on:click={() => showCrew = !showCrew}
                                                aria-expanded={showCrew}
                                            >
                                                <span>Crews</span>
                                                <svg class="w-5 h-5 transition-transform" style="transform: rotate({showCrew ? 90 : 0}deg);" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                                </svg>
                                            </button>
                                            {#if showCrew}
                                                <div transition:slide>
                                                    <div class="flex flex-wrap gap-4">
                                                        {#each data.crews.slice(0, 12) as crew}
                                                            <a href={`https://www.themoviedb.org/search?query=${crew.name}`} target="_blank">
                                                                <div class="flex flex-col items-center w-24">
                                                                    {#if crew.profile_path}
                                                                        <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden mb-2">
                                                                            <img
                                                                                src={`https://image.tmdb.org/t/p/w45/${crew.profile_path}`}
                                                                                alt={crew.name}
                                                                                class="w-full h-full object-cover"
                                                                                on:error={(e) => {
                                                                                    if (e.target instanceof HTMLImageElement) {
                                                                                        e.target.src = '/default-people.png';
                                                                                    }
                                                                                }}
                                                                            />
                                                                        </div>
                                                                    {:else}
                                                                        <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden mb-2">
                                                                            <img
                                                                                src="/default-people.png"
                                                                                alt={crew.name}
                                                                                class="w-full h-full object-cover"
                                                                            />
                                                                        </div>
                                                                    {/if}
                                                                    <div class="text-slate-200 text-sm font-bold text-center">{crew.name}</div>
                                                                    <div class="text-slate-200/70 text-xs text-center">{crew.job}</div>
                                                                </div>
                                                            </a>
                                                        {/each}
                                                    </div>
                                                </div>
                                            {/if}
                                        </div>
                                    </div>
                                {/if}

                                <!-- Production Companies Accordion -->
                                {#if data.production_companies && data.production_companies.length > 0}
                                    <div class="text-black-200/50 mb-4">
                                        <div class="mt-6">
                                            <button
                                                class="flex items-center gap-2 text-slate-200 text-xl font-bold mb-4 focus:outline-none"
                                                on:click={() => showProductionCompanies = !showProductionCompanies}
                                                aria-expanded={showProductionCompanies}
                                            >
                                                <span>Production Companies</span>
                                                <svg class="w-5 h-5 transition-transform" style="transform: rotate({showProductionCompanies ? 90 : 0}deg);" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                                </svg>
                                            </button>
                                            {#if showProductionCompanies}
                                                <div transition:slide>
                                                    <div class="flex flex-wrap gap-6">
                                                        {#each data.production_companies as company}
                                                            <div class="flex flex-col items-center w-24">
                                                                {#if company.logo_path}
                                                                    <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden p-2">
                                                                        <img
                                                                            src={`https://image.tmdb.org/t/p/w45/${company.logo_path}`}
                                                                            alt="{company.name} Logo"
                                                                            class="w-full h-full object-contain"
                                                                            on:error={(e) => {
                                                                                if (e.target instanceof HTMLImageElement) {
                                                                                    e.target.src = '/movie-camera.png';
                                                                                }
                                                                            }}
                                                                        />
                                                                    </div>
                                                                {:else}
                                                                    <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden mb-2">
                                                                        <img
                                                                            src="/movie-camera.png"
                                                                            alt="{company.name}"
                                                                            class="w-full h-full object-cover"
                                                                        />
                                                                    </div>
                                                                {/if}
                                                                <div class="text-slate-200 text-sm font-bold text-center mt-2">{company.name}</div>
                                                            </div>
                                                        {/each}
                                                    </div>
                                                </div>
                                            {/if}
                                        </div>
                                    </div>
                                {/if}

                                <!-- Videos Accordion (Clips, Featurettes, etc.) -->
                                {#if data.videos && data.videos.results && data.videos.results.length > 0}
                                    <div class="text-slate-200/50 mb-4">
                                        <div class="mt-6">
                                            <button
                                                class="flex items-center gap-2 text-slate-200 text-xl font-bold mb-4 focus:outline-none"
                                                on:click={() => showVideos = !showVideos}
                                                aria-expanded={showVideos}
                                            >
                                                <span>Extras</span>
                                                <svg class="w-5 h-5 transition-transform" style="transform: rotate({showVideos ? 90 : 0}deg);" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                                </svg>
                                            </button>
                                            {#if showVideos}
                                                <div transition:slide>
                                                    <div class="flex flex-wrap gap-6">
                                                        {#each data.videos.results as video}
                                                            <div class="flex flex-col items-center w-80">
                                                                <div class="w-full aspect-video rounded-lg overflow-hidden bg-black mb-2">
                                                                    {#if video.site === 'YouTube' && video.key}
                                                                        <iframe
                                                                            class="w-full h-full"
                                                                            src={`https://www.youtube.com/embed/${video.key}`}
                                                                            title={video.name}
                                                                            frameborder="0"
                                                                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                                                            allowfullscreen
                                                                        ></iframe>
                                                                    {:else if video.site === 'Vimeo' && video.key}
                                                                        <iframe
                                                                            class="w-full h-full"
                                                                            src={`https://player.vimeo.com/video/${video.key}`}
                                                                            title={video.name}
                                                                            frameborder="0"
                                                                            allow="autoplay; fullscreen; picture-in-picture"
                                                                            allowfullscreen
                                                                        ></iframe>
                                                                    {:else}
                                                                        <a href={video.url} target="_blank" class="text-turquoise-400 underline">Watch video</a>
                                                                    {/if}
                                                                </div>
                                                                <div class="text-slate-200 text-sm font-bold text-center">{video.name}</div>
                                                                <div class="text-slate-400 text-xs text-center">{video.type}</div>
                                                            </div>
                                                        {/each}
                                                    </div>
                                                </div>
                                            {/if}
                                        </div>
                                    </div>
                                {/if}

                                <!-- Streaming Providers -->
                                {#if data.providers && data.providers.results && data.providers.results[userCountry]}
                                    <div class="text-slate-200/50 mb-4">
                                        <div class="mt-6">
                                            <div class="flex items-center justify-between mb-4">
                                                <button
                                                    class="flex items-center gap-2 text-slate-200 text-xl font-bold focus:outline-none"
                                                    on:click={() => showProviders = !showProviders}
                                                    aria-expanded={showProviders}
                                                >
                                                    <span>Where to Watch</span>
                                                    <svg class="w-5 h-5 transition-transform" style="transform: rotate({showProviders ? 90 : 0}deg);" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                                    </svg>
                                                </button>
                                                <img src="/justwatch.webp" alt="JustWatch" class="h-3 opacity-50" />
                                            </div>
                                            {#if showProviders}
                                                <div transition:slide>
                                                    {#if data.providers && data.providers.results && data.providers.results[userCountry]}
                                                        {@const countryData = data.providers.results[userCountry]}
                                                        <div class="flex flex-col gap-6">
                                                            {#if countryData.flatrate && countryData.flatrate.length > 0}
                                                                <div>
                                                                    <h4 class="text-slate-200 text-lg font-bold mb-3">Streaming</h4>
                                                                    <div class="flex flex-wrap gap-4">
                                                                        {#each countryData.flatrate as provider}
                                                                            <a href={countryData.link} target="_blank" class="flex flex-col items-center w-24">
                                                                                <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden p-2">
                                                                                    <img
                                                                                        src={`https://image.tmdb.org/t/p/w45/${provider.logo_path}`}
                                                                                        alt="{provider.provider_name} Logo"
                                                                                        class="w-full h-full object-contain"
                                                                                        on:error={(e) => {
                                                                                            if (e.target instanceof HTMLImageElement) {
                                                                                                e.target.src = '/movie-camera.png';
                                                                                            }
                                                                                        }}
                                                                                    />
                                                                                </div>
                                                                                <div class="text-slate-200 text-sm font-bold text-center mt-2">{provider.provider_name}</div>
                                                                            </a>
                                                                        {/each}
                                                                    </div>
                                                                </div>
                                                            {/if}
                                                            {#if countryData.rent && countryData.rent.length > 0}
                                                                <div>
                                                                    <h4 class="text-slate-200 text-lg font-bold mb-3">Rent</h4>
                                                                    <div class="flex flex-wrap gap-4">
                                                                        {#each countryData.rent as provider}
                                                                            <a href={countryData.link} target="_blank" class="flex flex-col items-center w-24">
                                                                                <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden p-2">
                                                                                    <img
                                                                                        src={`https://image.tmdb.org/t/p/w45/${provider.logo_path}`}
                                                                                        alt="{provider.provider_name} Logo"
                                                                                        class="w-full h-full object-contain"
                                                                                        on:error={(e) => {
                                                                                            if (e.target instanceof HTMLImageElement) {
                                                                                                e.target.src = '/movie-camera.png';
                                                                                            }
                                                                                        }}
                                                                                    />
                                                                                </div>
                                                                                <div class="text-slate-200 text-sm font-bold text-center mt-2">{provider.provider_name}</div>
                                                                            </a>
                                                                        {/each}
                                                                    </div>
                                                                </div>
                                                            {/if}
                                                            {#if countryData.buy && countryData.buy.length > 0}
                                                                <div>
                                                                    <h4 class="text-slate-200 text-lg font-bold mb-3">Buy</h4>
                                                                    <div class="flex flex-wrap gap-4">
                                                                        {#each countryData.buy as provider}
                                                                            <a href={countryData.link} target="_blank" class="flex flex-col items-center w-24">
                                                                                <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden p-2">
                                                                                    <img
                                                                                        src={`https://image.tmdb.org/t/p/w45/${provider.logo_path}`}
                                                                                        alt="{provider.provider_name} Logo"
                                                                                        class="w-full h-full object-contain"
                                                                                        on:error={(e) => {
                                                                                            if (e.target instanceof HTMLImageElement) {
                                                                                                e.target.src = '/movie-camera.png';
                                                                                            }
                                                                                        }}
                                                                                    />
                                                                                </div>
                                                                                <div class="text-slate-200 text-sm font-bold text-center mt-2">{provider.provider_name}</div>
                                                                            </a>
                                                                        {/each}
                                                                    </div>
                                                                </div>
                                                            {/if}
                                                        </div>
                                                    {/if}
                                                </div>
                                            {/if}
                                        </div>
                                    </div>
                                {/if}
                            </div>
                        </div>
                    </div>
                {/if}
            {/each}
        {/if}
    {:catch error}
        <div class="text-red-500 p-4">Failed to load movie details.</div>
    {/await}
</div>
